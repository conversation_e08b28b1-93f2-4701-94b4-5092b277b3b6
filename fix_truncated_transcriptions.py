#!/usr/bin/env python3
"""
Fix Truncated Transcriptions Script
Uses Whisper to re-transcribe audio files that have truncated text (ending with "...")
"""

import json
import os
import sys
from pathlib import Path
import whisper
from tqdm import tqdm

def load_transcriptions(progress_file):
    """Load transcriptions from progress.json and identify truncated ones"""
    with open(progress_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    all_transcriptions = {}
    truncated_files = {}
    
    for hash_id, info in data.get('processed_texts', {}).items():
        if not info.get('skipped', False) and 'text' in info and 'filepath' in info:
            all_transcriptions[hash_id] = {
                'text': info['text'],
                'filepath': info['filepath']
            }
            
            # Check if text is truncated (ends with "...")
            if info['text'].endswith('...'):
                truncated_files[hash_id] = {
                    'text': info['text'],
                    'filepath': info['filepath']
                }
    
    return all_transcriptions, truncated_files

def transcribe_with_whisper(audio_file, model):
    """Transcribe audio file using Whisper"""
    try:
        result = model.transcribe(str(audio_file), language='es')
        return result['text'].strip()
    except Exception as e:
        print(f"Error transcribing {audio_file}: {e}")
        return None

def update_transcription_file(transcription_file, hash_to_number_map, updated_transcriptions):
    """Update the transcription file with corrected transcriptions"""
    
    # Read current transcription file
    with open(transcription_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Update lines with corrected transcriptions
    updated_lines = []
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        # Parse line: wavs/N.wav|text
        parts = line.split('|', 1)
        if len(parts) != 2:
            updated_lines.append(line)
            continue
            
        wav_path, old_text = parts
        
        # Extract number from wav path (e.g., "wavs/123.wav" -> 123)
        try:
            number = int(wav_path.split('/')[-1].replace('.wav', ''))
        except:
            updated_lines.append(line)
            continue
        
        # Find corresponding hash_id for this number
        hash_id = None
        for hid, num in hash_to_number_map.items():
            if num == number:
                hash_id = hid
                break
        
        # Update text if we have a corrected transcription
        if hash_id and hash_id in updated_transcriptions:
            new_text = updated_transcriptions[hash_id]
            updated_lines.append(f"{wav_path}|{new_text}")
            print(f"Updated {wav_path}: {old_text[:50]}... -> {new_text[:50]}...")
        else:
            updated_lines.append(line)
    
    # Write updated transcription file
    with open(transcription_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(updated_lines) + '\n')

def main():
    # Configuration
    progress_file = Path('progress.json')
    transcription_file = Path('transcriptions.txt')
    
    print("Loading transcriptions and identifying truncated ones...")
    all_transcriptions, truncated_files = load_transcriptions(progress_file)
    
    print(f"Found {len(truncated_files)} truncated transcriptions out of {len(all_transcriptions)} total")
    
    if len(truncated_files) == 0:
        print("No truncated transcriptions found!")
        return
    
    # Load Whisper model
    print("Loading Whisper model (this may take a moment)...")
    model = whisper.load_model("base")  # You can use "small", "medium", "large" for better accuracy
    
    # Create mapping from hash_id to number (based on current transcription file)
    hash_to_number_map = {}
    with open(transcription_file, 'r', encoding='utf-8') as f:
        for i, line in enumerate(f, 1):
            line = line.strip()
            if line:
                # Find corresponding hash_id by matching the truncated text
                for hash_id, info in all_transcriptions.items():
                    if info['text'] in line:
                        hash_to_number_map[hash_id] = i
                        break
    
    # Re-transcribe truncated files
    updated_transcriptions = {}
    print("Re-transcribing truncated audio files...")
    
    for hash_id, info in tqdm(truncated_files.items(), desc="Transcribing"):
        audio_file = Path(info['filepath'])
        
        if not audio_file.exists():
            print(f"Audio file not found: {audio_file}")
            continue
        
        # Transcribe with Whisper
        new_text = transcribe_with_whisper(audio_file, model)
        
        if new_text:
            updated_transcriptions[hash_id] = new_text
            print(f"\nFixed: {info['text'][:50]}...")
            print(f"  -> {new_text[:50]}...")
        else:
            print(f"Failed to transcribe: {audio_file}")
    
    if updated_transcriptions:
        print(f"\nUpdating transcription file with {len(updated_transcriptions)} corrected transcriptions...")
        update_transcription_file(transcription_file, hash_to_number_map, updated_transcriptions)
        print("Transcription file updated successfully!")
    else:
        print("No transcriptions were successfully corrected.")
    
    print(f"\nSummary:")
    print(f"- Total files: {len(all_transcriptions)}")
    print(f"- Truncated files found: {len(truncated_files)}")
    print(f"- Successfully corrected: {len(updated_transcriptions)}")

if __name__ == "__main__":
    main()
