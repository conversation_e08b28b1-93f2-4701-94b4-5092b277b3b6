#!/usr/bin/env python3
"""
Fix Numbers in Transcriptions
Convert all digits to written words in Spanish for TTS dataset quality
"""

import re
from pathlib import Path

def number_to_spanish_words(num):
    """Convert a number to Spanish words"""
    
    # Basic numbers 0-99
    ones = ['', 'uno', 'dos', 'tres', 'cuatro', 'cinco', 'seis', 'siete', 'ocho', 'nueve',
            'diez', 'once', 'doce', 'trece', 'catorce', 'quince', 'dieciséis', 'diecisiete', 
            'dieciocho', 'diecinueve']
    
    tens = ['', '', 'veinte', 'treinta', 'cuarenta', 'cincuenta', 'sesenta', 'setenta', 'ochenta', 'noventa']
    
    hundreds = ['', 'ciento', 'doscientos', 'trescientos', 'cuatrocientos', 'quinientos', 
                'seiscientos', 'setecientos', 'ochocientos', 'novecientos']
    
    if num == 0:
        return 'cero'
    elif num == 100:
        return 'cien'
    elif num < 20:
        return ones[num]
    elif num < 30:
        if num == 20:
            return 'veinte'
        else:
            return 'veinti' + ones[num - 20]
    elif num < 100:
        ten = num // 10
        one = num % 10
        if one == 0:
            return tens[ten]
        else:
            return tens[ten] + ' y ' + ones[one]
    elif num < 1000:
        hundred = num // 100
        remainder = num % 100
        result = hundreds[hundred]
        if remainder > 0:
            result += ' ' + number_to_spanish_words(remainder)
        return result
    elif num < 10000:
        thousand = num // 1000
        remainder = num % 1000
        if thousand == 1:
            result = 'mil'
        else:
            result = number_to_spanish_words(thousand) + ' mil'
        if remainder > 0:
            result += ' ' + number_to_spanish_words(remainder)
        return result
    else:
        # For larger numbers, use a simpler approach
        return str(num)  # Fallback for very large numbers

def convert_time_expressions(text):
    """Convert time expressions like '1.42' to 'uno punto cuarenta y dos'"""
    # Match decimal numbers (like 1.42, 5.30, etc.)
    def replace_decimal(match):
        whole = int(match.group(1))
        decimal = match.group(2)
        
        whole_words = number_to_spanish_words(whole)
        decimal_words = ' '.join([number_to_spanish_words(int(d)) for d in decimal])
        
        return f"{whole_words} punto {decimal_words}"
    
    text = re.sub(r'(\d+)\.(\d+)', replace_decimal, text)
    return text

def convert_numbers_to_words(text):
    """Convert all standalone numbers in text to Spanish words"""
    
    # First handle time expressions and decimals
    text = convert_time_expressions(text)
    
    # Handle standalone numbers (not part of decimals)
    def replace_number(match):
        num = int(match.group())
        return number_to_spanish_words(num)
    
    # Match standalone numbers (not preceded or followed by dots or other numbers)
    text = re.sub(r'(?<!\d)(?<!\.)(\d+)(?!\d)(?!\.)', replace_number, text)
    
    return text

def fix_transcriptions_file(transcription_file):
    """Fix all numbers in the transcription file"""
    
    print(f"Processing {transcription_file}...")
    
    # Read all lines
    with open(transcription_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    fixed_lines = []
    changes_made = 0
    
    for i, line in enumerate(lines, 1):
        line = line.strip()
        if not line:
            fixed_lines.append('')
            continue
            
        # Parse line: wavs/N.wav|text
        parts = line.split('|', 1)
        if len(parts) != 2:
            fixed_lines.append(line)
            continue
            
        wav_path, text = parts
        original_text = text
        
        # Convert numbers to words
        fixed_text = convert_numbers_to_words(text)
        
        if fixed_text != original_text:
            changes_made += 1
            print(f"Line {i}: {original_text[:50]}...")
            print(f"    -> {fixed_text[:50]}...")
        
        fixed_lines.append(f"{wav_path}|{fixed_text}")
    
    # Write fixed file
    with open(transcription_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(fixed_lines) + '\n')
    
    print(f"\nCompleted! Made {changes_made} changes to transcriptions.")
    return changes_made

def main():
    transcription_file = Path('transcriptions.txt')
    
    if not transcription_file.exists():
        print(f"Error: {transcription_file} not found!")
        return
    
    print("Converting numbers to Spanish words in transcriptions...")
    print("This ensures proper TTS dataset quality.\n")
    
    changes = fix_transcriptions_file(transcription_file)
    
    if changes > 0:
        print(f"\n✅ Successfully converted {changes} transcriptions with numbers to words.")
        print("Your dataset now follows TTS best practices!")
    else:
        print("\n✅ No numbers found to convert. Dataset is already properly formatted!")

if __name__ == "__main__":
    main()
