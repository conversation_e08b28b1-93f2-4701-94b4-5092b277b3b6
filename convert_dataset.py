#!/usr/bin/env python3
"""
Dataset Conversion Script
Converts MP3 audio files to numbered WAV files and creates transcription file.
"""

import json
import os
import sys
from pathlib import Path
from pydub import AudioSegment
from tqdm import tqdm

def convert_mp3_to_wav(input_path, output_path, sample_rate=16000):
    """Convert MP3 to WAV with specified sample rate, 16-bit, mono using pydub"""
    try:
        # Load MP3 file
        audio = AudioSegment.from_mp3(str(input_path))

        # Convert to mono
        audio = audio.set_channels(1)

        # Set sample rate
        audio = audio.set_frame_rate(sample_rate)

        # Set to 16-bit
        audio = audio.set_sample_width(2)  # 2 bytes = 16 bits

        # Export as WAV
        audio.export(str(output_path), format="wav")
        return True
    except Exception as e:
        print(f"Error converting {input_path}: {e}")
        return False

def load_transcriptions(progress_file):
    """Load transcriptions from progress.json"""
    with open(progress_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    transcriptions = {}
    for hash_id, info in data.get('processed_texts', {}).items():
        if not info.get('skipped', False) and 'text' in info and 'filepath' in info:
            transcriptions[hash_id] = {
                'text': info['text'],
                'filepath': info['filepath']
            }
    
    return transcriptions

def main():
    # Configuration
    audio_input_dir = Path('audio_output')
    wavs_output_dir = Path('wavs')
    progress_file = Path('progress.json')
    transcription_file = Path('transcriptions.txt')
    sample_rate = 16000  # You can change this to 22050 if needed

    print("Using pydub for audio conversion (no ffmpeg required)")
    
    # Check if input files exist
    if not progress_file.exists():
        print(f"Error: {progress_file} not found!")
        sys.exit(1)
    
    if not audio_input_dir.exists():
        print(f"Error: {audio_input_dir} directory not found!")
        sys.exit(1)
    
    # Create output directory
    wavs_output_dir.mkdir(exist_ok=True)
    
    # Load transcriptions
    print("Loading transcriptions...")
    transcriptions = load_transcriptions(progress_file)
    print(f"Found {len(transcriptions)} transcriptions")
    
    # Filter transcriptions to only include existing audio files
    valid_transcriptions = {}
    for hash_id, info in transcriptions.items():
        audio_file = Path(info['filepath'])
        if audio_file.exists():
            valid_transcriptions[hash_id] = info
        else:
            print(f"Warning: Audio file not found: {audio_file}")
    
    print(f"Found {len(valid_transcriptions)} valid audio-transcription pairs")
    
    if len(valid_transcriptions) == 0:
        print("No valid audio files found!")
        sys.exit(1)
    
    # Convert files and create transcription list
    transcription_lines = []
    successful_conversions = 0
    
    # Sort by hash_id for consistent ordering
    sorted_items = sorted(valid_transcriptions.items())
    
    print("Converting audio files...")
    for i, (hash_id, info) in enumerate(tqdm(sorted_items), 1):
        input_path = Path(info['filepath'])
        output_filename = f"{i}.wav"
        output_path = wavs_output_dir / output_filename
        
        # Convert MP3 to WAV
        if convert_mp3_to_wav(input_path, output_path, sample_rate):
            # Add to transcription file
            transcription_line = f"wavs/{output_filename}|{info['text']}"
            transcription_lines.append(transcription_line)
            successful_conversions += 1
        else:
            print(f"Failed to convert {input_path}")
    
    # Write transcription file
    print("Writing transcription file...")
    with open(transcription_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(transcription_lines))
    
    print(f"\nConversion completed!")
    print(f"Successfully converted: {successful_conversions} files")
    print(f"Output directory: {wavs_output_dir}")
    print(f"Transcription file: {transcription_file}")
    print(f"Audio format: WAV, {sample_rate}Hz, 16-bit, mono")
    
    # Show sample of transcription file
    print(f"\nSample from {transcription_file}:")
    with open(transcription_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
        for i, line in enumerate(lines[:5]):
            print(f"  {line.strip()}")
        if len(lines) > 5:
            print(f"  ... and {len(lines) - 5} more lines")

if __name__ == "__main__":
    main()
