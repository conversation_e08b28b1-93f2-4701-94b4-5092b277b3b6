#!/usr/bin/env python3
"""
Fix Remaining Truncated Transcriptions
Re-transcribe the remaining files that still have "..." endings
"""

import json
import os
import sys
from pathlib import Path
import whisper
from tqdm import tqdm

def load_transcriptions_from_file(transcription_file):
    """Load transcriptions from the transcription file and find remaining truncated ones"""
    truncated_entries = []
    
    with open(transcription_file, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if not line:
                continue
                
            # Parse line: wavs/N.wav|text
            parts = line.split('|', 1)
            if len(parts) != 2:
                continue
                
            wav_path, text = parts
            
            # Check if text ends with "..."
            if text.endswith('...'):
                # Extract number from wav path
                try:
                    number = int(wav_path.split('/')[-1].replace('.wav', ''))
                    truncated_entries.append({
                        'line_num': line_num,
                        'wav_path': wav_path,
                        'number': number,
                        'old_text': text,
                        'full_line': line
                    })
                except:
                    continue
    
    return truncated_entries

def transcribe_with_whisper(audio_file, model):
    """Transcribe audio file using Whisper"""
    try:
        result = model.transcribe(str(audio_file), language='es')
        return result['text'].strip()
    except Exception as e:
        print(f"Error transcribing {audio_file}: {e}")
        return None

def update_transcription_file_lines(transcription_file, updates):
    """Update specific lines in the transcription file"""
    
    # Read all lines
    with open(transcription_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Apply updates
    for line_num, new_text in updates.items():
        if 1 <= line_num <= len(lines):
            # Find the wav path from the original line
            old_line = lines[line_num - 1].strip()
            if '|' in old_line:
                wav_path = old_line.split('|', 1)[0]
                lines[line_num - 1] = f"{wav_path}|{new_text}\n"
    
    # Write updated file
    with open(transcription_file, 'w', encoding='utf-8') as f:
        f.writelines(lines)

def main():
    # Configuration
    transcription_file = Path('transcriptions.txt')
    wavs_dir = Path('wavs')
    
    print("Loading remaining truncated transcriptions...")
    truncated_entries = load_transcriptions_from_file(transcription_file)
    
    print(f"Found {len(truncated_entries)} remaining truncated transcriptions")
    
    if len(truncated_entries) == 0:
        print("No remaining truncated transcriptions found!")
        return
    
    # Load Whisper model
    print("Loading Whisper model...")
    model = whisper.load_model("base")
    
    # Re-transcribe truncated files
    updates = {}
    successful_corrections = 0
    
    print("Re-transcribing remaining truncated audio files...")
    
    for entry in tqdm(truncated_entries, desc="Transcribing"):
        wav_file = wavs_dir / f"{entry['number']}.wav"
        
        if not wav_file.exists():
            print(f"Audio file not found: {wav_file}")
            continue
        
        # Transcribe with Whisper
        new_text = transcribe_with_whisper(wav_file, model)
        
        if new_text and new_text != entry['old_text']:
            updates[entry['line_num']] = new_text
            successful_corrections += 1
            print(f"\nFixed line {entry['line_num']}: {entry['old_text'][:50]}...")
            print(f"  -> {new_text[:50]}...")
        else:
            print(f"No improvement for line {entry['line_num']}")
    
    if updates:
        print(f"\nUpdating transcription file with {len(updates)} corrected transcriptions...")
        update_transcription_file_lines(transcription_file, updates)
        print("Transcription file updated successfully!")
    else:
        print("No transcriptions were successfully corrected.")
    
    print(f"\nSummary:")
    print(f"- Remaining truncated files found: {len(truncated_entries)}")
    print(f"- Successfully corrected: {successful_corrections}")
    
    # Final check
    final_truncated_count = 0
    with open(transcription_file, 'r', encoding='utf-8') as f:
        for line in f:
            if line.strip().endswith('...'):
                final_truncated_count += 1
    
    print(f"- Remaining truncated after correction: {final_truncated_count}")

if __name__ == "__main__":
    main()
