# Dataset Conversion Summary

## Overview
Successfully converted your audio dataset from MP3 format with hash-based filenames to the required numbered WAV format with transcriptions.

## Dataset Statistics
- **Total audio files**: 3,749
- **Audio format**: WAV, 16000 Hz, 16-bit, mono
- **Transcription format**: Single-speaker dataset format

## File Structure
```
andreadataset2/
├── wavs/                    # Numbered WAV files
│   ├── 1.wav
│   ├── 2.wav
│   ├── 3.wav
│   └── ... (3,749 files total)
├── transcriptions.txt       # Transcription file
├── audio_output/           # Original MP3 files (can be deleted)
├── progress.json           # Original transcription data
└── convert_dataset.py      # Conversion script
```

## Audio Specifications
- **Sample Rate**: 16,000 Hz
- **Bit Depth**: 16-bit
- **Channels**: Mono (1 channel)
- **Format**: WAV (PCM signed 16-bit little-endian)

## Transcription File Format
The `transcriptions.txt` file follows the required single-speaker dataset format:
```
wavs/1.wav|¿Tenés algunos otros requisitos?
wavs/2.wav|Quizá es una buena idea meter a tu hijo a una tutoría después de clases.
wavs/3.wav|En ese canal hay documentales a esas horas.
...
```

Each line contains:
- Path to the WAV file (relative to the dataset root)
- Pipe separator (`|`)
- Transcription text (what the character says in the audio)

## Sample Transcriptions
Here are the first 10 entries:
1. `wavs/1.wav|¿Tenés algunos otros requisitos?`
2. `wavs/2.wav|Quizá es una buena idea meter a tu hijo a una tutoría después de clases.`
3. `wavs/3.wav|En ese canal hay documentales a esas horas.`
4. `wavs/4.wav|Casablanca es la película favorita de personas nacidas entre mil novecientos ochenta y mil novecient...`
5. `wavs/5.wav|Las estadísticas muestran que hay más accidentes de carro los fines de semana que entre semana.`
6. `wavs/6.wav|Después de la hora tienes que regresar a una de las estaciones de bicicleta.`
7. `wavs/7.wav|El otro día vi un video en Facebook sobre un aparatito que te ponías en la oreja y directamente te t...`
8. `wavs/8.wav|¿Quiere que le dé el teléfono?`
9. `wavs/9.wav|Los requisitos para la escuela de cine es un examen en tres etapas, y una audición, imagínate`
10. `wavs/10.wav|¿Me conviene comprar el teléfono de Apple o de Samsung?`

## Language
The dataset appears to be in Spanish, with various conversational phrases and questions.

## Usage
Your dataset is now ready to use for:
- Text-to-Speech (TTS) training
- Voice cloning
- Speech synthesis models
- Any other audio-text paired machine learning tasks

## Files You Can Delete (Optional)
To save space, you can delete:
- `audio_output/` directory (original MP3 files)
- `audio_output 2.zip` (original zip file)
- `__MACOSX/` directory (macOS metadata)

## Conversion Script
The `convert_dataset.py` script is included for reference and can be reused for similar conversions in the future.
